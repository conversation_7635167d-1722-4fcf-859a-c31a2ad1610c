# PoolingSystem Performance Optimizations

## Overview
This document outlines the critical performance optimizations applied to the PoolingSystem to resolve FPS drops and garbage collection issues identified in the Unity Profiler.

## Issues Identified

### 🔴 Critical Performance Problems:
1. **RebuildTypeHashMap() called every frame** - causing massive GC allocations
2. **Multiple temp allocations in ProcessSpawnRequests()** - creating GC pressure
3. **Excessive EntityManager operations** in hierarchy processing
4. **Redundant component handle updates** - 19 handles updated every frame regardless of usage

## Optimizations Applied

### 1. Intelligent Hash Map Rebuilding
**Before:**
```csharp
// Called EVERY frame regardless of changes
private void RebuildTypeHashMap(ref SystemState state)
{
    var chunks = m_PooledEntityQuery.ToArchetypeChunkArray(Allocator.Temp); // GC ALLOCATION
    // ... processing
    chunks.Dispose(); // TEMP ALLOCATION DISPOSED EVERY FRAME
}
```

**After:**
```csharp
// Only rebuild when entities are actually added/removed
private void RebuildTypeHashMapIfNeeded(ref SystemState state)
{
    int currentPooledEntityCount = m_PooledEntityQuery.CalculateEntityCount();
    
    if (!m_HashMapNeedsRebuild && currentPooledEntityCount == m_LastPooledEntityCount)
    {
        return; // No changes, skip rebuild
    }
    
    // Use job-based approach to avoid temp allocations
    var job = new RebuildHashMapJob { /* ... */ };
    job.ScheduleParallel(m_PooledEntityQuery, state.Dependency).Complete();
}
```

### 2. Reusable Collections for Spawn Processing
**Before:**
```csharp
// New allocations every spawn request processing
using var reservedEntities = new NativeHashSet<Entity>(spawnRequests.Length, Allocator.Temp);
using var maxByType = new NativeHashMap<uint, int>(poolEntries.Length, Allocator.Temp);
using var countByType = new NativeHashMap<uint, int>(poolEntries.Length, Allocator.Temp);
```

**After:**
```csharp
// Reuse persistent collections, just clear them
m_ReusableReservedEntities.Clear();
m_ReusableMaxByType.Clear();
m_ReusableCountByType.Clear();
```

### 3. Conditional Handle Updates
**Before:**
```csharp
// Updated ALL 19 handles every frame
private void UpdateHandlesAndLookups(ref SystemState state)
{
    m_PoolTypeHandle.Update(ref state);
    m_PooledHandle.Update(ref state);
    // ... 17 more updates regardless of usage
}
```

**After:**
```csharp
// Only update handles that are actually needed
private void UpdateRequiredHandlesAndLookups(ref SystemState state, bool hasSpawnRequests, bool hasReturnRequests, bool hasPoolInitialization)
{
    m_PoolTypeHandle.Update(ref state); // Always needed for hash map
    
    if (hasSpawnRequests || hasReturnRequests)
    {
        m_PooledHandle.Update(ref state);
        // ... only update what's needed
    }
}
```

### 4. Early Exit Optimization
**Before:**
```csharp
public void OnUpdate(ref SystemState state)
{
    // Always processed all operations
    UpdateHandlesAndLookups(ref state);
    RebuildTypeHashMap(ref state);
    // ... all operations every frame
}
```

**After:**
```csharp
public void OnUpdate(ref SystemState state)
{
    // Check if we need to process anything at all
    bool hasSpawnRequests = !m_SpawnRequestQuery.IsEmpty;
    bool hasReturnRequests = !m_ReturnRequestQuery.IsEmpty;
    bool hasPoolInitialization = !m_PoolManagerQuery.IsEmpty;
    
    // Early exit if no work to do
    if (!hasSpawnRequests && !hasReturnRequests && !hasPoolInitialization)
    {
        ProcessHierarchyOperations(ref state);
        return;
    }
}
```

### 5. Job-Based Collection Operations
**Before:**
```csharp
using var entities = query.ToEntityArray(Allocator.Temp); // GC allocation
using var poolTypes = m_PoolCountQuery.ToComponentDataArray<PoolTypeComponent>(Allocator.Temp); // GC allocation
```

**After:**
```csharp
// Use jobs to populate reusable collections
var collectJob = new CollectManagerEntitiesJob
{
    ManagerEntities = m_TempEntityList, // Reusable persistent collection
    EntityTypeHandle = state.GetEntityTypeHandle()
};
collectJob.ScheduleParallel(query, state.Dependency).Complete();
```

## Performance Impact

### Expected Improvements:
- **🚀 Reduced GC Allocations**: Eliminated per-frame temp allocations
- **⚡ Faster Frame Times**: Early exit when no pooling operations needed
- **💾 Lower Memory Pressure**: Reusing persistent collections instead of temp allocations
- **🔧 Smarter Updates**: Only updating required component handles
- **📊 Incremental Processing**: Hash map only rebuilt when entities change

### Monitoring Points:
1. **GC.Alloc** in Unity Profiler should show significant reduction
2. **PoolingSystem.OnUpdate** execution time should decrease
3. **Memory allocations** per frame should be minimal
4. **Frame rate stability** should improve during heavy pooling operations

## Additional Optimizations Added

### New Persistent Collections:
```csharp
// Performance optimization: track when hash map needs rebuilding
private bool m_HashMapNeedsRebuild;
private int m_LastPooledEntityCount;

// Cached collections for spawn processing to avoid per-frame allocations
private NativeHashSet<Entity> m_ReusableReservedEntities;
private NativeHashMap<uint, int> m_ReusableMaxByType;
private NativeHashMap<uint, int> m_ReusableCountByType;
```

### Burst-Compiled Jobs:
- `RebuildHashMapJob` - Parallel hash map rebuilding
- `CollectManagerEntitiesJob` - Efficient entity collection
- `CountPoolTypesJob` - Parallel pool type counting

## Testing Recommendations

1. **Profile Before/After**: Compare Unity Profiler results
2. **Stress Test**: Spawn/return large numbers of entities
3. **Memory Monitoring**: Watch for GC spikes during pooling operations
4. **Frame Rate Analysis**: Monitor FPS during heavy pooling scenarios

## Conclusion

These optimizations address the root causes of performance issues:
- Eliminated unnecessary per-frame allocations
- Reduced redundant operations
- Implemented intelligent caching and early exits
- Used job system for parallel processing

The system should now scale much better with entity count and provide stable performance without GC spikes.
