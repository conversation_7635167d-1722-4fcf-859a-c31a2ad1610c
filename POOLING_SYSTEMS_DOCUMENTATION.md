# Pooling Systems Architecture Documentation

## Overview

This document provides a comprehensive analysis of the three interconnected pooling systems in the Unity DOTS project:

1. **PoolingSystem** - Core high-performance entity pooling system
2. **DropPoolingSystem** - Specialized system for drop resource management
3. **SpawnerSystem** - Entity spawning system with dual-mode operation (pooled/non-pooled)

## System Architecture

### 1. PoolingSystem (Core Pooling Engine)

**Location**: `Assets/Samples/CopyToMainProject/Scripts/Pooling/PoolingSystem.cs`

**Purpose**: High-performance DOTS-based entity pooling with advanced optimizations

#### Key Features:
- **Burst-compiled hot paths** with managed fallbacks for complex operations
- **Zero-allocation entity lookup** via hash maps
- **Cached component handles and queries** for maximum performance
- **Parallel processing support** for spawn/return operations
- **Hierarchy management** for complex prefabs with children

#### Core Components:
```csharp
// Pool management components
public struct PoolManagerTag : IComponentData { }
public struct Pooled : IComponentData, IEnableableComponent { }
public struct InUse : IComponentData, IEnableableComponent { }
public struct Disabled : IComponentData { }
public struct PoolTypeComponent : IComponentData { uint TypeHash; }

// Pool configuration
public struct PoolEntry : IBufferElementData
{
    public Entity Prefab;
    public uint TypeHash;
    public int PoolSize;      // Backward-compatible total size
    public int InitialSize;   // Pre-instantiated entities
    public int MaxSize;       // Hard cap for total instances
}

// Request/Response buffers
public struct PoolSpawnRequest : IBufferElementData
{
    public uint TypeHash;
    public Entity RequestingEntity;
    public float3 Position;
    public quaternion Rotation;
    public float Scale;
}

public struct PoolReturnRequest : IBufferElementData
{
    public Entity EntityToReturn;
}
```

#### System Flow:
1. **Initialization**: Pre-instantiate entities based on `InitialSize`
2. **Spawn Processing**: 
   - Check type hash map for available pooled entities
   - If none available, create new entity (within `MaxSize` limit)
   - Activate entity by toggling `Pooled`/`InUse` states
   - Handle hierarchy enabling for complex prefabs
3. **Return Processing**:
   - Deactivate entity by toggling states
   - Reset to original state (materials, physics, etc.)
   - Handle hierarchy disabling

#### Performance Optimizations:
- **Pre-allocated collections** reused every frame to avoid GC
- **Type hash to entity mapping** for O(1) entity lookup
- **Cached component handles** updated once per frame
- **Burst compilation** for critical paths
- **Parallel job processing** for spawn/return operations

### 2. DropPoolingSystem (Drop Resource Management)

**Location**: `Assets/Samples/CopyToMainProject/Scripts/ECS/DropResource/Systems/DropPoolingSystem.cs`

**Purpose**: Integrates drop spawning with the pooling system for currency, weapons, and health items

#### Key Features:
- **Integration with DamageSystem** for automatic drop generation
- **Configurable drop rates** per enemy type
- **Animation support** for drop effects (scale-up, movement)
- **Burst-optimized processing** where possible

#### Core Components:
```csharp
// Drop request from DamageSystem
public struct DropRequest : IComponentData
{
    public EnemyType EnemyType;
    public float3 DeathPosition;
    public Entity DeadEnemyEntity;
    public bool IsElite;
}

// Drop resource entity
public struct DropResourceComponent : IComponentData
{
    public DropableItemType DropType;
    public CurrencyType CurrencyType;
    public WeaponSubModuleState WeaponType;
    public int Amount;
    public float SpawnTime;
    public bool IsPickedUp;
}

// Animation and movement
public struct DropAnimationComponent : IComponentData
{
    public float StartTime;
    public float Duration;
    public float StartScale;
    public float TargetScale;
    public DropAnimationType AnimationType;
    public bool IsActive;
    public bool IsAnimating;
}

public struct DropMovementComponent : IComponentData
{
    public float3 StartPosition;
    public float3 TargetPosition;
    public float StartTime;
    public float Duration;
    public bool IsActive;
}
```

#### System Flow:
1. **Drop Request Processing**:
   - Receive `DropRequest` from `DamageSystem`
   - Look up enemy drop configuration
   - Calculate drop amounts based on enemy type and elite status
   - Create `PoolSpawnRequest` entries for each drop type
2. **Pool Integration**:
   - Add spawn requests to pool manager's buffer
   - Use type hashes to identify drop types
   - Let `PoolingSystem` handle actual entity spawning
3. **Drop Enhancement**:
   - Add animation components for visual effects
   - Set up movement patterns for drop spreading
   - Configure pickup behavior and timers

### 3. SpawnerSystem (Entity Spawning)

**Location**: `Assets/Scripts/_ECS/Systems/Spawner/SpawnerSystem.cs`

**Purpose**: Dual-mode spawning system supporting both pooled and direct instantiation

#### Key Features:
- **Dual operation modes**: Pooled vs. non-pooled spawning
- **Continuous spawning support** with count tracking
- **Navigation system integration** for AI entities
- **Spawner linking** for entity lifecycle tracking

#### Core Components:
```csharp
// Spawner components (two variants)
public struct Spawner : IComponentData
{
    public Entity Prefab;
    public int Count;
    public int MaxCount;
    public int Batch;
    public float Interval;
    public float Elapsed;
    public float3 Size;
    public float3 Destination;
    public Entity Group;
    public float PrefabSize;
    public bool ContinuousSpawning;
    public Random Random;
    public EnemyType EntityType;  // For pooled mode
}

public struct NormalSpawner : IComponentData
{
    // Similar structure for non-pooled mode
}

// Entity tracking
public struct SpawnerLink : IComponentData
{
    public Entity SpawnerEntity;
    public FixedString32Bytes SpawnerName;
}

// Navigation reset marker
public struct NavigationResetTag : IComponentData { }
```

#### System Flow:

**Pooled Mode** (when `UsePoolingSystemTag` exists):
1. **Count Synchronization**:
   - Count active entities with `SpawnerLink` and `InUse`
   - Update spawner counts for continuous spawning
2. **Pool Spawning**:
   - Use `PoolingUtility.GetEnumHash()` for type hash calculation
   - Create `PoolSpawnRequest` entries
   - Add to pool manager's buffer
3. **Navigation Reset**:
   - Reset navigation components for newly spawned entities
   - Toggle navigation components to force reinitialization

**Non-Pooled Mode**:
1. **Direct Instantiation**:
   - Use `SpawnerJob` for parallel processing
   - Instantiate prefabs directly via ECB
   - Set up navigation and crowd components
2. **Entity Tracking**:
   - Add `SpawnerLink` for lifecycle tracking
   - Add `SpawnComponent` for detection system grace period

## System Interconnections

### 1. PoolingSystem ↔ SpawnerSystem
- **SpawnerSystem** creates `PoolSpawnRequest` entries
- **PoolingSystem** processes these requests and spawns entities
- **SpawnerSystem** handles navigation reset for pooled entities
- Shared use of `PoolManagerTag` entity for coordination

### 2. PoolingSystem ↔ DropPoolingSystem
- **DropPoolingSystem** creates `PoolSpawnRequest` entries for drops
- **PoolingSystem** handles the actual entity spawning
- Both systems use the same pool manager entity
- Type hash system ensures correct prefab selection

### 3. SpawnerSystem ↔ DropPoolingSystem
- **Indirect connection** through shared pooling infrastructure
- Both can spawn entities that may later generate drops
- **SpawnerLink** component tracks entity origins for drop attribution

## Data Flow Diagram

```
DamageSystem → DropRequest → DropPoolingSystem → PoolSpawnRequest → PoolingSystem → Spawned Drops
                                                                                      ↑
SpawnerSystem → PoolSpawnRequest → PoolingSystem → Spawned Enemies → Death → DropRequest
```

## Performance Considerations

### PoolingSystem Optimizations:
- **Burst compilation** for critical paths
- **Pre-allocated native collections** to avoid GC
- **Cached component handles** updated once per frame
- **Hash map lookups** for O(1) entity retrieval
- **Parallel job processing** for spawn/return operations

### DropPoolingSystem Optimizations:
- **Burst compilation** where possible (limited by managed dependencies)
- **Batch processing** of drop requests
- **Efficient random number generation** with per-thread seeds

### SpawnerSystem Optimizations:
- **Conditional system execution** based on pooling mode
- **Efficient count tracking** with native hash maps
- **Parallel job execution** for non-pooled spawning

## Configuration and Setup

### Pool Manager Setup:
1. Create entity with `PoolManagerTag`
2. Add `PoolEntry` buffer with prefab configurations
3. Set `InitialSize`, `MaxSize`, and type hashes
4. System automatically initializes pools on first update

### Drop Configuration:
1. Configure `DropConfigurationComponent` singleton
2. Set up `EnemyDropConfigurationElement` buffer for each enemy type
3. Define `DropTypeHashElement` buffer for drop type mappings
4. System processes drop requests automatically

### Spawner Configuration:
1. Create entities with `Spawner` or `NormalSpawner` components
2. Set spawning parameters (count, interval, batch size)
3. Configure prefabs and destinations
4. Add `UsePoolingSystemTag` singleton to enable pooled mode

## Error Handling and Debugging

### Common Issues:
1. **Missing Pool Manager**: Systems log warnings when pool manager not found
2. **Type Hash Mismatches**: Ensure consistent hash calculation across systems
3. **Navigation Reset Failures**: Check for proper component setup on prefabs
4. **Memory Leaks**: Verify proper disposal of native collections

### Debug Features:
- Extensive logging in editor builds
- Component validation in editor mode
- Internal consistency checks
- Performance profiling markers

## Component Dependencies and Relationships

### PoolingSystem Dependencies:
```csharp
// Required Components
- PoolManagerTag (singleton entity)
- PoolEntry (buffer on pool manager)
- PoolSpawnRequest (buffer on pool manager)
- PoolReturnRequest (buffer on pool manager)

// Entity State Components
- Pooled (IEnableableComponent)
- InUse (IEnableableComponent)
- Disabled (marker for inactive entities)
- PoolTypeComponent (type identification)

// Hierarchy Management
- LinkedEntityGroup (Unity built-in)
- Child (Unity built-in)
- Parent (Unity built-in)
- NeedsHierarchyDisabled/Enabled/Check (internal markers)

// Integration Components
- SpawnerLink (tracks entity origin)
- NavigationRefreshNeeded (navigation system integration)
- JustActivatedFromPool (marker for newly activated entities)
```

### DropPoolingSystem Dependencies:
```csharp
// Input Components
- DropRequest (from DamageSystem)
- DropConfigurationComponent (singleton)
- EnemyDropConfigurationElement (buffer)
- DropTypeHashElement (buffer)

// Output Components
- DropResourceComponent (drop entity data)
- DropAnimationComponent (visual effects)
- DropMovementComponent (physics behavior)
- DropPickupComponent (interaction behavior)
- DropResourceTag (marker)

// Pooling Integration
- PoolSpawnRequest (sent to PoolingSystem)
- PoolManagerTag (target entity)
```

### SpawnerSystem Dependencies:
```csharp
// Spawner Components
- Spawner (pooled mode)
- NormalSpawner (non-pooled mode)
- UsePoolingSystemTag (mode selector singleton)

// Entity Tracking
- SpawnerLink (entity origin tracking)
- SpawnComponent (detection grace period)
- NavigationResetTag (navigation system integration)

// Navigation Integration
- AgentBody (ProjectDawn Navigation)
- AgentCollider, AgentSeparation, AgentReciprocalAvoid
- AgentCrowdPath (crowd behavior)

// Pooling Integration
- PoolManagerTag (pool manager reference)
- PoolSpawnRequest (pooled spawning)
- InUse (entity state tracking)
```

## System Update Order and Timing

### Update Groups and Ordering:
```csharp
// PoolingSystem
[UpdateInGroup(typeof(InitializationSystemGroup))]
- Runs early to process spawn/return requests
- Initializes pools before other systems need them

// DropPoolingSystem
[UpdateInGroup(typeof(SimulationSystemGroup))]
[UpdateAfter(typeof(DamageSystem))]
- Processes drop requests after damage is calculated
- Creates pool spawn requests for PoolingSystem

// SpawnerSystem
[UpdateInGroup(typeof(FixedStepSimulationSystemGroup))]
[UpdateBefore(typeof(AgentSystemGroup))]
- Runs at fixed timestep for consistent spawning
- Spawns entities before navigation system processes them
```

### Execution Flow:
1. **FixedStepSimulationSystemGroup**: SpawnerSystem creates spawn requests
2. **InitializationSystemGroup**: PoolingSystem processes spawn requests
3. **SimulationSystemGroup**: DropPoolingSystem processes drop requests
4. **Next Frame**: Newly spawned entities are available for other systems

## Memory Management and Performance

### Native Collection Usage:
```csharp
// PoolingSystem (Persistent Collections)
- NativeParallelMultiHashMap<uint, Entity> m_TypeHashToEntityMap
- NativeList<Entity> m_TempEntityList
- NativeList<PoolTypeComponent> m_TempPoolTypeList
- NativeHashMap<uint, int> m_TypeHashCounts

// Temporary Collections (Per-Frame)
- NativeHashMap<uint, int> typeHashCounts (Allocator.Temp)
- NativeHashSet<Entity> reservedEntities (Allocator.Temp)
- ArchetypeChunkArray chunks (Allocator.Temp)
```

### Memory Optimization Strategies:
1. **Pre-allocation**: Collections sized based on expected usage
2. **Reuse**: Persistent collections cleared and reused each frame
3. **Temporary Allocation**: Short-lived collections use Temp allocator
4. **Disposal**: Automatic cleanup in OnDestroy and using statements

### Performance Profiling Points:
- **Pool initialization time** (one-time cost)
- **Spawn request processing** (per-frame cost)
- **Entity activation/deactivation** (per-entity cost)
- **Hierarchy management** (complex prefabs only)
- **Type hash lookup** (O(1) operation)

## Integration with Other Systems

### Navigation System Integration:
- **SpawnerSystem** resets navigation components for pooled entities
- **AgentBody** destination set from spawner configuration
- **Navigation components** toggled to force reinitialization
- **NavigationRefreshNeeded** marker for delayed activation

### Damage System Integration:
- **DamageSystem** creates `DropRequest` entities
- **DropPoolingSystem** processes requests and creates drops
- **Entity death** triggers drop generation automatically

### Animation System Integration:
- **Pooled entities** maintain animation state
- **Ragdoll system** integration for death animations
- **Material restoration** for visual effects reset

### UI System Integration:
- **Health bars** track pooled entity reuse
- **Drop pickup** UI feedback systems
- **Spawner status** debugging displays

## Troubleshooting Guide

### Common Issues and Solutions:

#### 1. Entities Not Spawning
**Symptoms**: No entities appear despite spawn requests
**Causes**:
- Missing PoolManagerTag entity
- Incorrect type hash calculation
- Pool exhausted (reached MaxSize limit)
**Solutions**:
- Verify pool manager setup in scene
- Check type hash consistency between systems
- Increase MaxSize or implement pool expansion

#### 2. Navigation Not Working on Pooled Entities
**Symptoms**: Entities spawn but don't move correctly
**Causes**:
- Navigation components not reset properly
- Missing navigation component setup
- Timing issues with component activation
**Solutions**:
- Verify ResetNavigationForPooledEntities execution
- Check prefab navigation component setup
- Add NavigationRefreshNeeded marker

#### 3. Memory Leaks
**Symptoms**: Increasing memory usage over time
**Causes**:
- Native collections not disposed
- Entities not returned to pool properly
- Temporary allocations not cleaned up
**Solutions**:
- Verify OnDestroy implementation
- Check return-to-pool logic
- Use memory profiler to identify leaks

#### 4. Performance Issues
**Symptoms**: Frame rate drops during spawning
**Causes**:
- Too many entities spawned per frame
- Inefficient component queries
- Missing Burst compilation
**Solutions**:
- Reduce batch sizes
- Optimize entity queries
- Enable Burst compilation where possible

## Future Improvements

### Potential Optimizations:
1. **GPU-based spawning** for massive entity counts
2. **Spatial partitioning** for more efficient entity lookup
3. **Predictive pooling** based on gameplay patterns
4. **Memory pool optimization** for reduced fragmentation

### Feature Enhancements:
1. **Dynamic pool resizing** based on usage patterns
2. **Cross-scene pool persistence** for seamless transitions
3. **Advanced animation systems** for drop effects
4. **Hierarchical spawning** for complex entity relationships

## Conclusion

The three pooling systems work together to provide a comprehensive entity management solution:

- **PoolingSystem** provides the core high-performance pooling infrastructure
- **DropPoolingSystem** specializes in drop resource management with visual effects
- **SpawnerSystem** offers flexible spawning with both pooled and direct modes

This architecture enables efficient entity reuse, reduces garbage collection pressure, and provides the foundation for scalable gameplay systems. The modular design allows each system to be optimized independently while maintaining clean interfaces between components.
